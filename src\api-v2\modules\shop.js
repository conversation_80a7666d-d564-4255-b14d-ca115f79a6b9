/**
 * 订单管理模块 - V2版本
 * 按照API封装规范文档实现订单管理相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取订单统计数据
   * @returns {Promise} 返回订单统计数据
   */
  orderCensus() {
    console.log('📊 订单统计API-V2请求')
    return get('/ims/api/admin/order/census')
  },

  /**
   * 获取订单列表 - 使用POST请求
   * @param {Object} querys 查询参数
   * @param {string} querys.orderCode 订单号，非必填
   * @param {string} querys.goodsName 商品名称，非必填
   * @param {string} querys.coachName 师傅姓名，非必填
   * @param {number} querys.payType 支付类型，非必填
   * @param {number} querys.type 订单类型，非必填
   * @param {string} querys.startTime 开始时间，非必填
   * @param {string} querys.endTime 结束时间，非必填
   * @param {string} querys.address 地址，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认20
   * @returns {Promise} 返回订单列表数据
   */
  orderList(querys) {
    console.log('📋 订单列表API-V2请求参数:', querys)
    return post('/api/admin/order/list', querys)
  },

  /**
   * 导出订单列表
   * @param {Object} querys 查询参数
   * @param {string} querys.orderCode 订单号，非必填
   * @param {string} querys.goodsName 商品名称，非必填
   * @param {string} querys.coachName 师傅姓名，非必填
   * @param {number} querys.payType 支付类型，非必填
   * @param {number} querys.type 订单类型，非必填
   * @param {string} querys.startTime 开始时间，非必填
   * @param {string} querys.endTime 结束时间，非必填
   * @param {string} querys.address 地址，非必填
   * @returns {Promise} 返回导出结果
   */
  orderExport(querys) {
    console.log('📤 导出订单列表API-V2请求参数:', querys)
    return post('/api/admin/order/export', querys)
  },

  /**
   * 获取订单详情
   * @param {number} orderId 订单ID
   * @returns {Promise} 返回订单详情数据
   */
  orderDetail(orderId) {
    if (!orderId) {
      return Promise.reject(new Error('订单ID不能为空'))
    }
    console.log('🔍 获取订单详情API-V2请求:', orderId)
    return get(`/api/admin/order/detail/${orderId}`)
  },

  /**
   * 师傅收入排行榜
   * @param {Object} querys 查询参数
   * @param {number} querys.top 排行数量，可选，默认5
   * @param {string} querys.startTime 开始时间，可选
   * @param {string} querys.endTime 结束时间，可选
   * @returns {Promise} 返回师傅收入排行榜数据
   */
  coachIncomeRank(querys) {
    console.log('💰 师傅收入排行榜API-V2请求参数:', querys)
    return get('/api/admin/order/coach-income', querys)
  },

  /**
   * 师傅跑单排行榜
   * @param {Object} querys 查询参数
   * @param {number} querys.top 排行数量，可选，默认5
   * @param {string} querys.startTime 开始时间，可选
   * @param {string} querys.endTime 结束时间，可选
   * @returns {Promise} 返回师傅跑单排行榜数据
   */
  coachCancelRank(querys) {
    console.log('🏃 师傅跑单排行榜API-V2请求参数:', querys)
    return get('/api/admin/order/coach-cancel', querys)
  },

  /**
   * 用户跑单排行榜
   * @param {Object} querys 查询参数
   * @param {number} querys.top 排行数量，可选，默认5
   * @param {string} querys.startTime 开始时间，可选
   * @param {string} querys.endTime 结束时间，可选
   * @returns {Promise} 返回用户跑单排行榜数据
   */
  userCancelRank(querys) {
    console.log('👤 用户跑单排行榜API-V2请求参数:', querys)
    return get('/api/admin/order/user-cancel', querys)
  },

  /**
   * 退款列表查询
   * @param {Object} querys 查询参数
   * @param {string} querys.goodsName 服务项目名，可选
   * @param {string} querys.orderCode 订单号，可选
   * @param {string} querys.status 状态，可选，0全部 1已退款 2申请中 3已拒绝
   * @param {string} querys.pageNum 页数，可选
   * @param {string} querys.pageSize 数量，可选
   * @returns {Promise} 返回退款列表数据
   */
  refundList(querys) {
    console.log('💰 退款列表查询API-V2请求参数:', querys)
    return get('/api/admin/refund/list', querys)
  },

  /**
   * 更新订单状态
   * @param {Object} querys 状态更新参数
   * @param {number} querys.id 订单ID
   * @param {number} querys.status 订单状态
   * @param {string} querys.remark 备注信息
   * @returns {Promise} 返回更新结果
   */
  orderStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('订单ID不能为空'))
    }

    console.log('🔄 更新订单状态API-V2请求:', querys)
    return post(`/api/admin/order/status/${querys.id}`, querys)
  },

  /**
   * 取消订单
   * @param {Object} querys 取消参数
   * @param {number} querys.id 订单ID
   * @param {string} querys.reason 取消原因
   * @returns {Promise} 返回取消结果
   */
  orderCancel(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('订单ID不能为空'))
    }

    console.log('❌ 取消订单API-V2请求:', querys)
    return post(`/api/admin/order/cancel/${querys.id}`, querys)
  },

  /**
   * 获取退款列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 退款状态，1申请中，2已同意，3已拒绝，4已退款
   * @param {string} querys.orderNo 订单号，非必填
   * @param {string} querys.userPhone 用户手机号，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回退款列表数据
   */
  refundList(querys) {
    console.log('💰 退款列表API-V2请求参数:', querys)
    return get('/api/admin/refund/list', querys)
  },

  /**
   * 获取退款详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 退款ID
   * @returns {Promise} 返回退款详情数据
   */
  refundInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('退款ID不能为空'))
    }
    console.log('🔍 获取退款详情API-V2请求:', querys)
    return get(`/api/admin/refund/info/${querys.id}`)
  },

  /**
   * 处理退款申请
   * @param {Object} querys 处理参数
   * @param {number} querys.id 退款ID
   * @param {number} querys.status 处理状态，2同意，3拒绝
   * @param {string} querys.remark 处理备注
   * @returns {Promise} 返回处理结果
   */
  refundHandle(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('退款ID不能为空'))
    }

    if (![2, 3].includes(querys.status)) {
      return Promise.reject(new Error('处理状态无效'))
    }

    console.log('✅ 处理退款申请API-V2请求:', querys)
    return post(`/api/admin/refund/handle/${querys.id}`, querys)
  },

  // ==================== 订单退款管理相关接口 ====================

  /**
   * 后台审核订单退款列表
   * @param {Object} querys 查询参数
   * @param {string} querys.goodsName 服务项目名，可选
   * @param {string} querys.orderCode 订单号，可选
   * @param {number} querys.status 状态，可选，0全部 1申请中 2已退款 3已驳回
   * @param {string} querys.pageNum 页数，可选
   * @param {string} querys.pageSize 数量，可选
   * @returns {Promise} 返回订单退款列表数据
   */
  orderRefundList(querys) {
    console.log('💰 订单退款列表API-V2请求参数:', querys)
    return get('/api/admin/order/refundList', querys)
  },

  /**
   * 同意退款
   * @param {Object} querys 同意退款参数
   * @param {number} querys.id 申请退款记录id，必需
   * @param {number} querys.price 退款金额，必需
   * @param {string} querys.text 备注文本，可选
   * @returns {Promise} 返回同意退款结果
   */
  passRefund(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('申请退款记录ID不能为空'))
    }
    if (querys.price === undefined || querys.price === null) {
      return Promise.reject(new Error('退款金额不能为空'))
    }

    console.log('✅ 同意退款API-V2请求:', querys)
    return post('/api/admin/order/passRefund', querys)
  },

  /**
   * 拒绝退款
   * @param {Object} querys 拒绝退款参数
   * @param {number} querys.id 申请退款记录id，必需
   * @param {number} querys.price 退款金额，必需
   * @param {string} querys.text 备注文本，必需
   * @returns {Promise} 返回拒绝退款结果
   */
  noPassRefund(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('申请退款记录ID不能为空'))
    }
    if (querys.price === undefined || querys.price === null) {
      return Promise.reject(new Error('退款金额不能为空'))
    }
    if (!querys.text) {
      return Promise.reject(new Error('备注文本不能为空'))
    }

    console.log('❌ 拒绝退款API-V2请求:', querys)
    return post('/api/admin/order/noPassRefund', querys)
  },

  /**
   * 获取订单退款详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 退款记录ID，必需
   * @returns {Promise} 返回退款详情数据
   */
  orderRefundDetail(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('退款记录ID不能为空'))
    }
    console.log('🔍 获取订单退款详情API-V2请求:', querys)
    return get(`/api/admin/order/refundDetail/${querys.id}`)
  },

  /**
   * 差价同意退款
   * @param {Object} querys 差价同意退款参数
   * @param {number} querys.id 差价退款记录id，必需
   * @param {number} querys.price 退款金额，必需
   * @param {string} querys.text 备注文本，可选
   * @returns {Promise} 返回差价同意退款结果
   */
  diffPassRefund(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('差价退款记录ID不能为空'))
    }
    if (querys.price === undefined || querys.price === null) {
      return Promise.reject(new Error('退款金额不能为空'))
    }

    console.log('✅ 差价同意退款API-V2请求:', querys)
    return post('/api/admin/order/diffPassRefund', querys)
  },

  /**
   * 差价拒绝退款
   * @param {Object} querys 差价拒绝退款参数
   * @param {number} querys.id 差价退款记录id，必需
   * @param {number} querys.price 退款金额，必需
   * @param {string} querys.text 拒绝原因，必需
   * @returns {Promise} 返回差价拒绝退款结果
   */
  diffNoPassRefund(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('差价退款记录ID不能为空'))
    }
    if (querys.price === undefined || querys.price === null) {
      return Promise.reject(new Error('退款金额不能为空'))
    }
    if (!querys.text) {
      return Promise.reject(new Error('拒绝原因不能为空'))
    }

    console.log('❌ 差价拒绝退款API-V2请求:', querys)
    return post('/api/admin/order/diffNoPassRefund', querys)
  },

  /**
   * 获取评价列表
   * @param {Object} querys 查询参数
   * @param {number} querys.userId 用户id，非必填
   * @param {number} querys.coachId 师傅id，非必填
   * @param {number} querys.orderId 订单id，非必填
   * @param {number} querys.star 星级，非必填
   * @param {string} querys.text 评价内容，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回评价列表数据
   */
  commentList(querys) {
    console.log('⭐ 评价列表API-V2请求参数:', querys)
    return get('/api/admin/comment/list', querys)
  },

  /**
   * 获取评价详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 评价ID
   * @returns {Promise} 返回评价详情数据
   */
  commentDetail(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('评价ID不能为空'))
    }
    console.log('🔍 获取评价详情API-V2请求:', querys)
    return get(`/api/admin/comment/detail/${querys.id}`)
  },

  /**
   * 删除评价
   * @param {Object} querys 删除参数
   * @param {number} querys.id 评价ID
   * @returns {Promise} 返回删除结果
   */
  commentDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('评价ID不能为空'))
    }

    console.log('🗑️ 删除评价API-V2请求:', querys)
    return post(`/api/admin/comment/delete/${querys.id}`)
  },

  /**
   * 修改评价状态
   * @param {Object} querys 状态修改参数
   * @param {number} querys.id 评价ID
   * @returns {Promise} 返回状态修改结果
   */
  commentStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('评价ID不能为空'))
    }

    console.log('🔄 修改评价状态API-V2请求:', querys)
    return post(`/api/admin/comment/status/${querys.id}`)
  },

  /**
   * 获取分销佣金列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，1待结算，2已结算
   * @param {string} querys.distributorPhone 分销商手机号，非必填
   * @param {string} querys.orderNo 订单号，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回佣金列表数据
   */
  commissionList(querys) {
    console.log('💵 分销佣金列表API-V2请求参数:', querys)
    return post('/api/admin/commission/list', querys)
  },

  /**
   * 结算分销佣金
   * @param {Object} querys 结算参数
   * @param {number} querys.id 佣金记录ID
   * @returns {Promise} 返回结算结果
   */
  commissionSettle(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('佣金记录ID不能为空'))
    }

    console.log('💰 结算分销佣金API-V2请求:', querys)
    return post(`/api/admin/commission/settle/${querys.id}`)
  },

  /**
   * 申请佣金提现
   * @param {Object} data 提现申请数据
   * @param {number} data.amount 提现金额
   * @param {number} data.method 提现方式
   * @param {string} data.remark 备注
   * @returns {Promise} 返回提现申请结果
   */
  commissionWithdraw(data) {
    console.log('💸 申请佣金提现API-V2请求:', data)
    return post('/api/admin/commission/withdraw', data)
  },

  /**
   * 获取售后列表
   * @param {Object} querys 查询参数
   * @param {number} querys.type 售后类型，1退货，2换货，3维修
   * @param {number} querys.status 状态，1申请中，2处理中，3已完成，4已拒绝
   * @param {string} querys.orderNo 订单号，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回售后列表数据
   */
  afterSaleList(querys) {
    console.log('🔧 售后列表API-V2请求参数:', querys)
    return get('/api/admin/aftersale/list', querys)
  },

  /**
   * 处理售后申请
   * @param {Object} querys 处理参数
   * @param {number} querys.id 售后ID
   * @param {number} querys.status 处理状态
   * @param {string} querys.remark 处理备注
   * @returns {Promise} 返回处理结果
   */
  afterSaleHandle(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('售后ID不能为空'))
    }

    console.log('✅ 处理售后申请API-V2请求:', querys)
    return post(`/api/admin/aftersale/handle/${querys.id}`, querys)
  }
}
