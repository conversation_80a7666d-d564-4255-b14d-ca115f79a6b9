<!--
  卡券编辑页面
 /src/view/market/edit.vue重构
-->

<template>
  <div class="lb-market-edit">
    <TopNav :title="navTitle" :isBack="true" />
    <div class="page-main">
      <el-form
        @submit.prevent
        :model="subForm"
        :rules="subFormRules"
        ref="subFormRef"
        label-width="140px"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="form-section-title">基本信息</div>

          <el-form-item label="卡券名称" prop="title">
            <el-input
              v-model="subForm.title"
              maxlength="20"
              show-word-limit
              placeholder="请输入卡券名称"
              style="width: 300px;"
            />
          </el-form-item>

          <el-form-item label="使用条件" prop="type">
            <el-radio-group v-model="subForm.type">
              <el-radio :value="0">消费满</el-radio>
              <el-radio :value="1">无门槛</el-radio>
            </el-radio-group>
          </el-form-item>
        
          <el-form-item prop="full">
            <template v-if="subForm.type === 0">
              消费满
              <el-input
                v-model="subForm.full"
                placeholder="请输入消费金额"
                style="width: 130px; margin: 0 8px;"
              />
              元可用
            </template>
            <template v-else>立减</template>
            <el-input
              v-model="subForm.discount"
              placeholder="请输入优惠金额"
              style="width: 130px; margin: 0 8px;"
            />
            <template v-if="subForm.type === 1">元</template>
          </el-form-item>

          <el-form-item label="使用规则" prop="rule">
            <el-input
              type="textarea"
              :rows="4"
              maxlength="1000"
              resize="none"
              show-word-limit
              placeholder="请输入使用规则"
              v-model="subForm.rule"
              style="width: 500px;"
            />
          </el-form-item>

          <el-form-item label="优惠详情" prop="text">
            <el-input
              type="textarea"
              :rows="4"
              maxlength="1000"
              resize="none"
              show-word-limit
              placeholder="请输入优惠详情"
              v-model="subForm.text"
              style="width: 500px;"
            />
          </el-form-item>
        </div>

        <!-- 派发设置 -->
        <div class="form-section">
          <div class="form-section-title">派发设置</div>

          <el-form-item label="派发方式" prop="sendType">
            <el-radio-group
              v-model="subForm.sendType"
              :disabled="subForm.id ? true : false"
            >
              <el-radio :value="0">活动派发</el-radio>
              <el-radio :value="2">用户领取</el-radio>
              <el-radio :value="1">平台定向派发</el-radio>
            </el-radio-group>
            <LbToolTips>
              <div>活动派发：需要参与活动，活动参与成功之后系统自动派发到用户的卡包</div>
              <div style="margin-top: 8px;">平台定向派发：平台指定派发到用户的卡包</div>
            </LbToolTips>
          </el-form-item>

          <el-form-item label="领取身份" prop="userLimit" v-if="subForm.sendType == 2">
            <el-radio-group v-model="subForm.userLimit">
              <el-radio :value="1">不限制，任何人可领取</el-radio>
              <el-radio :value="2">仅限新用户</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="卡券库存" prop="stock">
            <el-input-number
              v-model="subForm.stock"
              :min="0"
              :controls="false"
              placeholder="请输入卡券数量"
              style="width: 200px;"
            />
          </el-form-item>
        </div>

        <!-- 时间设置 -->
        <div class="form-section">
          <div class="form-section-title">时间设置</div>

          <el-form-item label="使用时间" prop="timeLimit">
            <el-radio-group v-model="subForm.timeLimit">
              <el-radio :value="0">指定日期</el-radio>
              <el-radio :value="1">有效天数</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="开始时间" prop="startTime" v-if="subForm.timeLimit === 0">
            <el-date-picker
              v-model="subForm.startTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择开始日期时间"
              :teleported="false"
              style="width: 300px;"
            />
          </el-form-item>

          <el-form-item label="结束时间" prop="endTime" v-if="subForm.timeLimit === 0">
            <el-date-picker
              v-model="subForm.endTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择结束日期时间"
              :teleported="false"
              style="width: 300px;"
            />
            <LbToolTips>领取的卡券必须在该时间前使用</LbToolTips>
          </el-form-item>

          <el-form-item label="有效天数" prop="day" v-if="subForm.timeLimit === 1">
            <div style="display: flex; align-items: center;">
              <span style="margin-right: 8px;">自领券当日起</span>
              <el-input-number
                v-model="subForm.day"
                :controls="false"
                :min="1"
                :precision="0"
                style="width: 120px; margin: 0 8px;"
              />
              <span style="margin-right: 8px;">天内可用</span>
              <LbToolTips>
                有效期按自然天计算。
                <div style="margin: 8px 0;">
                  举例：如设置领券当日起30天内可用，用户在5月18日14:00时领取卡券，则该卡券的可用时间为5月18日的14:00:00至6月18日的14:00
                </div>
                注意：时间按自然天来算，不是月
              </LbToolTips>
            </div>
          </el-form-item>
        </div>
        
        <!-- 其他设置 -->
        <div class="form-section">
          <div class="form-section-title">其他设置</div>

          <el-form-item label="排序值" prop="top">
            <el-input-number
              v-model="subForm.top"
              :min="0"
              :controls="false"
              placeholder="请输入排序值"
              style="width: 200px;"
            />
            <LbToolTips>值越大, 排序越靠前</LbToolTips>
          </el-form-item>
        
          <el-form-item label="限用项目" prop="goodsIds">
            <LbButton type="primary" @click="showServiceDialog = true">
              选择项目
            </LbButton>

            <!-- 已选择的项目列表 -->
            <div v-if="selectedServices.length > 0" style="margin-top: 16px;">
              <div style="margin-bottom: 8px; font-weight: 500; color: #606266;">
                已选择项目 ({{ selectedServices.length }}个):
              </div>
              <el-table
                :data="selectedServices"
                size="small"
                border
                style="width: 100%;"
                max-height="200"
                class="selected-services-table"
              >
                <el-table-column prop="id" label="项目ID" width="100" align="center" />
                <el-table-column prop="name" label="项目名称" min-width="150" />
                <el-table-column label="操作" width="80" align="center">
                  <template #default="scope">
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeService(scope.row.id)"
                      link
                    >
                      移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div v-else style="margin-top: 8px; color: #909399; font-size: 14px;">
              暂未选择任何项目
            </div>
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-section">
          <el-form-item>
            <LbButton
              type="primary"
              @click="submitForm"
              :loading="submitLoading"
              size="default"
              style="width: 120px;"
            >
              {{ isEdit ? '更新卡券' : '新增卡券' }}
            </LbButton>
            <LbButton
              @click="$router.go(-1)"
              style="margin-left: 12px; width: 80px;"
              size="default"
            >
              取消
            </LbButton>
          </el-form-item>
        </div>
      </el-form>
    </div>
    
    <!-- 选择项目对话框 -->
    <el-dialog v-model="showServiceDialog" title="选择限用项目" width="70%">
      <!-- 搜索表单 -->
      <div style="margin-bottom: 16px;">
        <el-form :inline="true">
          <el-form-item label="项目名称">
            <el-input
              v-model="serviceSearchForm.name"
              placeholder="请输入项目名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <LbButton type="primary" @click="getServiceList">搜索</LbButton>
            <LbButton @click="resetServiceSearch">重置</LbButton>
          </el-form-item>
        </el-form>
      </div>

      <!-- 服务分类表格 -->
      <el-table
        v-loading="serviceLoading"
        :data="serviceList"
        @selection-change="handleServiceSelection"
        ref="serviceTableRef"
        max-height="400"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="项目ID" width="100" align="center" />
        <el-table-column prop="name" label="项目名称" min-width="200" />
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination
          v-model:current-page="serviceSearchForm.pageNum"
          v-model:page-size="serviceSearchForm.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="serviceTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleServiceSizeChange"
          @current-change="handleServiceCurrentChange"
          :pager-count="5"
          prev-text="上一页"
          next-text="下一页"
          :small="false"
          background
        />
      </div>

      <template #footer>
        <LbButton @click="showServiceDialog = false">取消</LbButton>
        <LbButton type="primary" @click="confirmServiceSelection">确定</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbToolTips from '@/components/common/LbToolTips.vue'
import { useEditOperations } from '@/composables/useDataRefresh'

const route = useRoute()
const router = useRouter()

// 获取Vue实例，用于访问this.$api
const { proxy } = getCurrentInstance()

// 使用编辑操作 Composable
const { handleAddSuccess, handleEditSuccess } = useEditOperations({
  module: 'market',
  router,
  redirectPath: '/market/list'
})

// 响应式数据
const subFormRef = ref()
const serviceTableRef = ref()
const submitLoading = ref(false)
const showServiceDialog = ref(false)
const serviceLoading = ref(false)
const serviceList = ref([])
const serviceTotal = ref(0)
const selectedServices = ref([])
const tempSelectedServices = ref([])

// 服务分类搜索表单
const serviceSearchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  name: ''
})

// 是否编辑模式
const isEdit = computed(() => !!route.query.id)
const navTitle = computed(() => isEdit.value ? '编辑卡券' : '新增卡券')

// 表单数据
const subForm = reactive({
  id: null,
  title: '',
  type: 0,
  full: '',
  discount: '',
  rule: '',
  text: '',
  sendType: 0,  // 修改为驼峰命名，与API保持一致
  userLimit: 1, // 修改为驼峰命名，与API保持一致
  stock: 0,
  timeLimit: 0, // 修改为驼峰命名，与API保持一致
  startTime: '', // 修改为驼峰命名，与API保持一致
  endTime: '',   // 修改为驼峰命名，与API保持一致
  day: 1,
  top: 0,
  status: 1,
  goodsIds: []  // 添加限用项目ID数组
})

// 表单验证规则
const subFormRules = {
  title: [
    { required: true, message: '请输入卡券名称', trigger: 'blur' }
  ],
  discount: [
    { required: true, message: '请输入优惠金额', trigger: 'blur' }
  ],
  full: [
    {
      validator: (_rule, value, callback) => {
        if (subForm.type === 0 && (!value || value <= 0)) {
          callback(new Error('请输入消费金额'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  rule: [
    { required: true, message: '请输入使用规则', trigger: 'blur' }
  ],
  text: [
    { required: true, message: '请输入优惠详情', trigger: 'blur' }
  ],
  sendType: [
    { required: true, message: '请选择派发方式', trigger: 'change' }
  ],
  stock: [
    { required: true, message: '请输入卡券库存', trigger: 'blur' }
  ],
  timeLimit: [
    { required: true, message: '请选择使用时间类型', trigger: 'change' }
  ],
  startTime: [
    {
      validator: (_rule, value, callback) => {
        if (subForm.timeLimit === 0 && !value) {
          callback(new Error('请选择开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  endTime: [
    {
      validator: (_rule, value, callback) => {
        if (subForm.timeLimit === 0 && !value) {
          callback(new Error('请选择结束时间'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  day: [
    {
      validator: (_rule, value, callback) => {
        if (subForm.timeLimit === 1 && (!value || value <= 0)) {
          callback(new Error('请输入有效天数'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  top: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ]
}

// 方法
const getServiceList = async (flag) => {
  if (flag) serviceSearchForm.pageNum = 1

  serviceLoading.value = true
  try {
    // 直接获取所有服务分类，不限制参数
    const params = {
      pageNum: serviceSearchForm.pageNum,
      pageSize: serviceSearchForm.pageSize
    }

    // 添加可选参数
    if (serviceSearchForm.name) params.name = serviceSearchForm.name

    // 使用API-V2调用服务分类接口
    const result = await proxy.$api.service.serviceCateList(params)
    console.log('🔍 服务分类列表数据:', result)

    if (result.code === 200 || result.code === '200') {
      const data = result.data
      serviceList.value = data.list || data || []
      serviceTotal.value = data.totalCount || data.total || 0

      console.log('📊 处理后的服务分类数据:', {
        list: serviceList.value,
        total: serviceTotal.value
      })
    } else {
      console.error('❌ 服务分类API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取服务分类失败')
    }
  } catch (error) {
    console.error('获取服务分类列表失败:', error)
    ElMessage.error('获取服务分类失败')
  } finally {
    serviceLoading.value = false
  }
}

const getCouponDetail = async (id) => {
  try {
    // 使用API-V2获取优惠券详情
    const result = await proxy.$api.market.couponInfo({ id })
    console.log('🔍 优惠券详情数据:', result)

    if (result.code === 200 || result.code === '200') {
      const data = result.data
      // 填充表单数据
      subForm.id = data.id
      subForm.title = data.title || ''
      subForm.type = data.type || 0
      subForm.full = data.full || ''
      subForm.discount = data.discount || ''
      subForm.rule = data.rule || ''
      subForm.text = data.text || ''
      subForm.sendType = data.sendType || 0
      subForm.userLimit = data.userLimit || 1
      subForm.stock = data.stock || 0
      subForm.timeLimit = data.timeLimit || 0
      subForm.startTime = data.startTime || ''
      subForm.endTime = data.endTime || ''
      subForm.day = data.day || 1
      subForm.top = data.top || 0
      subForm.status = data.status || 1

      // 处理已选择的服务分类
      selectedServices.value = data.services || []
      subForm.goodsIds = selectedServices.value.map(service => service.id)
    } else {
      ElMessage.error(result.message || result.msg || '获取优惠券详情失败')
    }
  } catch (error) {
    console.error('获取优惠券详情失败:', error)
    ElMessage.error('获取优惠券详情失败')
  }
}

// 重置服务分类搜索
const resetServiceSearch = () => {
  serviceSearchForm.name = ''
  getServiceList(1)
}

// 分页处理
const handleServiceSizeChange = (size) => {
  serviceSearchForm.pageSize = size
  getServiceList(1)
}

const handleServiceCurrentChange = (page) => {
  serviceSearchForm.pageNum = page
  getServiceList()
}

// 不再需要格式化日期时间函数，因为表格中不再显示创建时间

const handleServiceSelection = (selection) => {
  tempSelectedServices.value = selection
}

const confirmServiceSelection = () => {
  selectedServices.value = [...tempSelectedServices.value]
  subForm.goodsIds = selectedServices.value.map(service => service.id)
  showServiceDialog.value = false
  ElMessage.success(`已选择 ${selectedServices.value.length} 个项目`)
}

const removeService = (serviceId) => {
  selectedServices.value = selectedServices.value.filter(service => service.id !== serviceId)
  subForm.goodsIds = selectedServices.value.map(service => service.id)
}

const submitForm = async () => {
  try {
    await subFormRef.value.validate()

    submitLoading.value = true

    // 准备提交数据
    const submitData = {
      ...subForm,
      goodsIds: subForm.goodsIds || []
    }

    console.log('📤 提交优惠券数据:', submitData)

    let result
    if (isEdit.value) {
      // 编辑优惠券
      result = await proxy.$api.market.couponUpdate(submitData)
    } else {
      // 新增优惠券
      result = await proxy.$api.market.couponAdd(submitData)
    }

    console.log('📥 优惠券提交结果:', result)

    if (result.code === 200 || result.code === '200') {
      // 使用统一的成功处理逻辑
      if (isEdit.value) {
        handleEditSuccess(submitData, '更新成功')
      } else {
        handleAddSuccess(submitData, '新增成功')
      }
    } else {
      ElMessage.error(result.message || result.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 初始化服务分类数据
  getServiceList()

  // 如果是编辑模式，获取优惠券详情
  if (isEdit.value && route.query.id) {
    getCouponDetail(route.query.id)
  }

  // 初始化表格选中状态
  if (serviceTableRef.value) {
    selectedServices.value.forEach(service => {
      const row = serviceList.value.find(item => item.id === service.id)
      if (row) {
        serviceTableRef.value.toggleRowSelection(row, true)
      }
    })
  }
})
</script>

<style scoped>
.lb-market-edit {
  padding: 0;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-main {
  max-width: 1200px;
  margin: 0;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin: 20px;
}

.el-form {
  margin-top: 0;
  background-color: #fff;
}

.el-form-item {
  margin-bottom: 24px;
}

.el-form-item__label {
  font-weight: 500;
  color: #303133;
  line-height: 40px;
}

.el-form-item__content {
  line-height: 40px;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 分页组件中文样式 */
:deep(.el-pagination) {
  .el-pagination__total {
    color: #606266;
    font-weight: 400;
  }

  .el-pagination__sizes .el-select .el-input__inner {
    height: 28px;
    line-height: 28px;
  }

  .btn-prev, .btn-next {
    background: #f4f4f5;
    color: #606266;
    border: 1px solid #dcdfe6;
  }

  .btn-prev:hover, .btn-next:hover {
    color: #409eff;
  }

  .el-pagination__jump {
    color: #606266;
  }

  .el-pagination__jump .el-input__inner {
    text-align: center;
  }
}

/* 已选择项目表格样式 */
.selected-services-table {
  margin-top: 12px;
}

.selected-services-table .el-table__header {
  background-color: #fafafa;
}

.selected-services-table .el-table th {
  background-color: #fafafa !important;
}

@media (max-width: 768px) {
  .lb-market-edit {
    padding: 10px;
  }

  .page-main {
    max-width: 100%;
  }

  .el-form {
    margin-top: 10px;
  }
}
</style>
