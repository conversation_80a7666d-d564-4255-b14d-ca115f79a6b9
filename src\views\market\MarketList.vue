<!--
  优惠券管理页面
  参考ServiceFenlei.vue的实现模式，按照快速开发指南重构
  对应API: /api/admin/coupon/*
-->

<template>
  <div class="market-list">
    <!-- 顶部导航 -->
    <TopNav title="优惠券管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="优惠券名称" prop="title">
                <el-input
                  size="default"
                  v-model="searchForm.title"
                  placeholder="请输入优惠券名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="可用" :value="1" />
                  <el-option label="不可用" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item label="类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择类型"
                  clearable
                  style="width: 140px"
                >
                  <el-option label="满减券" :value="0" />
                  <el-option label="无门槛券" :value="1" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增优惠券
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }"
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }"
          style="width: 100%"
        >
        <el-table-column prop="id" label="ID" width="100" align="center" />

        <el-table-column prop="title" label="优惠券名称" min-width="150" />

        <el-table-column prop="type" label="使用条件" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.type === 0">
              消费满¥{{ scope.row.full }}减¥{{ scope.row.discount }}
            </span>
            <span v-else>
              立减¥{{ scope.row.discount }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="sendType" label="派发方式" min-width="120">
          <template #default="scope">
            {{ getSendTypeText(scope.row.sendType) }}
          </template>
        </el-table-column>

        <el-table-column prop="stock" label="库存数量" width="100" align="center" />

        <el-table-column prop="haveSend" label="已发放" width="100" align="center" />

        <el-table-column prop="top" label="排序值" width="100" align="center" />

        <el-table-column prop="createTime" label="创建时间" min-width="120">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.createTime) }}</p>
              <p>{{ formatTime(scope.row.createTime) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="-1"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="table-operate">
              <LbButton
                size="default"
                type="primary"
                @click="handleEdit(scope.row)"
              >
                编辑
              </LbButton>
              <LbButton
                size="default"
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </LbButton>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优惠券名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入优惠券名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠券类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
                <el-option label="满减券" :value="0" />
                <el-option label="无门槛券" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.type === 0">
          <el-col :span="12">
            <el-form-item label="满减条件" prop="full">
              <el-input-number
                v-model="form.full"
                :min="0"
                :precision="2"
                placeholder="请输入满减条件金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠金额" prop="discount">
              <el-input-number
                v-model="form.discount"
                :min="0"
                :precision="2"
                placeholder="请输入优惠金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.type === 1">
          <el-col :span="12">
            <el-form-item label="优惠金额" prop="discount">
              <el-input-number
                v-model="form.discount"
                :min="0"
                :precision="2"
                placeholder="请输入优惠金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存数量" prop="stock">
              <el-input-number
                v-model="form.stock"
                :min="1"
                placeholder="请输入库存数量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="派发方式" prop="sendType">
              <el-select v-model="form.sendType" placeholder="请选择派发方式" style="width: 100%">
                <el-option label="活动派发" :value="0" />
                <el-option label="平台定向派发" :value="1" />
                <el-option label="用户领取" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序值" prop="top">
              <el-input-number
                v-model="form.top"
                :min="0"
                placeholder="请输入排序值"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户限制" prop="userLimit">
              <el-input-number
                v-model="form.userLimit"
                :min="1"
                placeholder="每用户可领取数量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :value="1">可用</el-radio>
                <el-radio :value="-1">不可用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="使用规则" prop="rule">
          <el-input v-model="form.rule" type="textarea" placeholder="请输入使用规则（可选）" />
        </el-form-item>

        <el-form-item label="优惠券描述" prop="text">
          <el-input v-model="form.text" type="textarea" placeholder="请输入优惠券描述（可选）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'
import { useListRefresh } from '@/composables/useDataRefresh'

// 直接导入API
import { api } from '@/api-v2'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  status: null,
  type: null
})

// 编辑表单
const form = reactive({
  id: null,
  title: '',
  type: 0,
  full: 0,
  discount: 0,
  sendType: 0,
  timeLimit: 0,
  startTime: '',
  endTime: '',
  day: 0,
  stock: 100,
  userLimit: 1,
  rule: '',
  text: '',
  top: 0,
  status: 1
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入优惠券名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择优惠券类型', trigger: 'change' }
  ],
  discount: [
    { required: true, message: '请输入优惠金额', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => form.id ? '编辑优惠券' : '新增优惠券')

// 方法
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.title) params.title = searchForm.title
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.type !== null && searchForm.type !== '') params.type = searchForm.type

    // 使用API-V2调用方式
    const result = await api.market.couponList(params)
    console.log('🎫 优惠券列表数据 (API-V2):', result)

    // 处理真实API的响应格式
    if (result.code === 200 || result.code === '200') {
      // 根据真实API数据结构处理
      const data = result.data
      const rawList = data.list || []
      tableData.value = rawList
      total.value = data.totalCount || data.total || 0

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.message || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取优惠券列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchForm.title = ''
  searchForm.status = null
  searchForm.type = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  // 跳转到新增优惠券页面
  router.push('/market/edit')
}

const getSendTypeText = (sendType) => {
  const typeMap = {
    0: '活动派发',
    1: '平台定向派发',
    2: '用户领取'
  }
  return typeMap[sendType] || '未知'
}

const handleEdit = (row) => {
  // 跳转到编辑优惠券页面，传递ID参数
  router.push(`/market/edit?id=${row.id}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除优惠券 "${row.title}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.market.couponDelete({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除优惠券失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleStatusChange = async (row) => {
  try {
    const result = await api.market.couponStatus({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('状态修改成功')
    } else {
      // 恢复原状态
      row.status = row.status === 1 ? -1 : 1
      ElMessage.error(result.message || '状态修改失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? -1 : 1
    console.error('修改状态失败:', error)
    ElMessage.error('状态修改失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    let result
    if (form.id) {
      // 编辑优惠券
      result = await api.market.couponUpdate(form)
    } else {
      // 新增优惠券
      result = await api.market.couponAdd(form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      // 使用统一的刷新方法
      refresh()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

const handleDialogClose = () => {
  resetForm()
}

const resetForm = () => {
  form.id = null
  form.title = ''
  form.type = 0
  form.full = 0
  form.discount = 0
  form.sendType = 0
  form.timeLimit = 0
  form.startTime = ''
  form.endTime = ''
  form.day = 0
  form.stock = 100
  form.userLimit = 1
  form.rule = ''
  form.text = ''
  form.top = 0
  form.status = 1
}

// 格式化日期时间
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 使用列表刷新 Composable
const { refresh } = useListRefresh({
  module: 'market',
  loadDataFn: getTableDataList,
  autoRefresh: true
})

// 生命周期
onMounted(() => {
  getTableDataList(1)
})
</script>

<style scoped>
/* 页面主体样式 */
.market-list {
  padding: 0px;
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表格边框增强 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 时间列样式 */
.time-column p {
  margin: 0;
  line-height: 1.4;
  font-size: 14px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

/* 操作按钮样式 */
.table-operate {
  display: flex;
  gap: 5px;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }

  .table-operate {
    flex-direction: column;
    gap: 2px;
  }
}
</style>
