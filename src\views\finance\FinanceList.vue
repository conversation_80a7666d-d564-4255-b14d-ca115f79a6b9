<!--
  财务流水管理页面 - FinanceList.vue
  基于接口文档实现完整的财务流水管理功能
  包含：流水列表、搜索筛选、导出等功能
-->

<template>
  <div class="page-container">
    <!-- 顶部导航 -->
    <TopNav title="财务流水管理" />

    <div class="content-container">
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ censusData.overall.incomeTotal.amount || '0.00' }}</div>
              <div class="stat-label">总收入</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ censusData.overall.payoutTotal.amount || '0.00' }}</div>
              <div class="stat-label">总支出</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ censusData.overall.netAmount || '0.00' }}</div>
              <div class="stat-label">净收入</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ censusData.today.incomeTotal.amount || '0.00' }}</div>
              <div class="stat-label">今日收入</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细统计卡片 -->
      <el-row :gutter="20" class="detail-stats-cards">
        <el-col :span="8">
          <el-card class="detail-stat-card">
            <template #header>
              <div class="card-header">
                <span>全量统计</span>
              </div>
            </template>
            <div class="stat-items">
              <div class="stat-item">
                <div class="stat-label">师傅服务收入</div>
                <div class="stat-value">¥{{ censusData.overall.serviceIncome.amount }}</div>
                <div class="stat-count">{{ censusData.overall.serviceIncome.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">佣金收入</div>
                <div class="stat-value">¥{{ censusData.overall.commissionIncome.amount }}</div>
                <div class="stat-count">{{ censusData.overall.commissionIncome.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">服务提现</div>
                <div class="stat-value">¥{{ censusData.overall.serviceWithdraw.amount }}</div>
                <div class="stat-count">{{ censusData.overall.serviceWithdraw.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">佣金提现</div>
                <div class="stat-value">¥{{ censusData.overall.commissionWithdraw.amount }}</div>
                <div class="stat-count">{{ censusData.overall.commissionWithdraw.count }}笔</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="detail-stat-card">
            <template #header>
              <div class="card-header">
                <span>今日统计</span>
              </div>
            </template>
            <div class="stat-items">
              <div class="stat-item">
                <div class="stat-label">师傅服务收入</div>
                <div class="stat-value">¥{{ censusData.today.serviceIncome.amount }}</div>
                <div class="stat-count">{{ censusData.today.serviceIncome.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">佣金收入</div>
                <div class="stat-value">¥{{ censusData.today.commissionIncome.amount }}</div>
                <div class="stat-count">{{ censusData.today.commissionIncome.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">服务提现</div>
                <div class="stat-value">¥{{ censusData.today.serviceWithdraw.amount }}</div>
                <div class="stat-count">{{ censusData.today.serviceWithdraw.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">佣金提现</div>
                <div class="stat-value">¥{{ censusData.today.commissionWithdraw.amount }}</div>
                <div class="stat-count">{{ censusData.today.commissionWithdraw.count }}笔</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="detail-stat-card">
            <template #header>
              <div class="card-header">
                <span>本月统计</span>
              </div>
            </template>
            <div class="stat-items">
              <div class="stat-item">
                <div class="stat-label">师傅服务收入</div>
                <div class="stat-value">¥{{ censusData.month.serviceIncome.amount }}</div>
                <div class="stat-count">{{ censusData.month.serviceIncome.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">佣金收入</div>
                <div class="stat-value">¥{{ censusData.month.commissionIncome.amount }}</div>
                <div class="stat-count">{{ censusData.month.commissionIncome.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">服务提现</div>
                <div class="stat-value">¥{{ censusData.month.serviceWithdraw.amount }}</div>
                <div class="stat-count">{{ censusData.month.serviceWithdraw.count }}笔</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">佣金提现</div>
                <div class="stat-value">¥{{ censusData.month.commissionWithdraw.amount }}</div>
                <div class="stat-count">{{ censusData.month.commissionWithdraw.count }}笔</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <!-- 搜索条件 -->
              <el-form-item label="流水类型" prop="type">
                <el-select
                  size="default"
                  v-model="searchForm.type"
                  placeholder="请选择流水类型"
                  clearable
                  style="width: 150px"
                >
                  <el-option label="服务收入" :value="0" />
                  <el-option label="分销" :value="1" />
                  <el-option label="服务提现" :value="2" />
                  <el-option label="佣金提现" :value="3" />
                </el-select>
              </el-form-item>

              <el-form-item label="用户ID" prop="userId">
                <el-input
                  size="default"
                  v-model="searchForm.userId"
                  placeholder="请输入用户ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="师傅ID" prop="coachId">
                <el-input
                  size="default"
                  v-model="searchForm.coachId"
                  placeholder="请输入师傅ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>

              <el-form-item label="时间范围" prop="timeRange">
                <el-date-picker
                  size="default"
                  v-model="timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 350px"
                  @change="handleTimeRangeChange"
                />
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                  :loading="loading"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="success"
                  icon="Download"
                  @click="handleExport"
                  :loading="exportLoading"
                >
                  导出
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontSize: '16px',
            fontWeight: '600'
          }"
          :cell-style="{
            fontSize: '14px',
            padding: '12px 8px'
          }"
          style="width: 100%"
          :fit="true"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="type" label="流水类型" min-width="120" align="center">
            <template #default="scope">
              <el-tag :type="getTypeTagType(scope.row.type)">
                {{ getTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="金额" min-width="150" align="center">
            <template #default="scope">
              <span :class="getPriceClass(scope.row.type, scope.row.price)">
                {{ formatPrice(scope.row.price) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="userId" label="用户ID" min-width="120" align="center">
            <template #default="scope">
              {{ scope.row.userId || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="coachId" label="师傅ID" min-width="120" align="center">
            <template #default="scope">
              {{ scope.row.coachId || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="180" />
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbPage from '@/components/common/LbPage.vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const timeRange = ref([])

// 统计数据
const censusData = ref({
  overall: {
    serviceIncome: { amount: 0, count: 0 },
    commissionIncome: { amount: 0, count: 0 },
    serviceWithdraw: { amount: 0, count: 0 },
    commissionWithdraw: { amount: 0, count: 0 },
    incomeTotal: { amount: 0, count: 0 },
    payoutTotal: { amount: 0, count: 0 },
    netAmount: 0
  },
  today: {
    serviceIncome: { amount: 0, count: 0 },
    commissionIncome: { amount: 0, count: 0 },
    serviceWithdraw: { amount: 0, count: 0 },
    commissionWithdraw: { amount: 0, count: 0 },
    incomeTotal: { amount: 0, count: 0 },
    payoutTotal: { amount: 0, count: 0 },
    netAmount: 0
  },
  month: {
    serviceIncome: { amount: 0, count: 0 },
    commissionIncome: { amount: 0, count: 0 },
    serviceWithdraw: { amount: 0, count: 0 },
    commissionWithdraw: { amount: 0, count: 0 },
    incomeTotal: { amount: 0, count: 0 },
    payoutTotal: { amount: 0, count: 0 },
    netAmount: 0
  }
})

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  type: null,
  userId: '',
  coachId: '',
  startTime: '',
  endTime: ''
})

// 搜索表单引用
const searchFormRef = ref(null)

/**
 * 获取流水类型文本
 */
const getTypeText = (type) => {
  const typeMap = {
    0: '服务收入',
    1: '分销',
    2: '服务提现',
    3: '佣金提现'
  }
  return typeMap[type] || '未知'
}

/**
 * 获取流水类型标签类型
 */
const getTypeTagType = (type) => {
  const typeMap = {
    0: 'success',  // 服务收入
    1: 'primary',  // 分销
    2: 'warning',  // 服务提现
    3: 'info'      // 佣金提现
  }
  return typeMap[type] || 'info'
}

/**
 * 格式化价格显示
 */
const formatPrice = (price) => {
  const amount = parseFloat(price) || 0
  return amount >= 0 ? `+¥${amount.toFixed(2)}` : `¥${amount.toFixed(2)}`
}

/**
 * 获取价格样式类
 */
const getPriceClass = (type, price) => {
  const amount = parseFloat(price) || 0
  // 提现类型显示为负数（红色），收入类型显示为正数（绿色）
  if (type === 2 || type === 3) {
    return 'price-negative'
  } else if (amount > 0) {
    return 'price-positive'
  }
  return 'price-neutral'
}

/**
 * 处理时间范围变化
 */
const handleTimeRangeChange = (value) => {
  if (value && value.length === 2) {
    searchForm.startTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

/**
 * 加载统计数据
 */
const loadCensusData = async () => {
  try {
    console.log('📊 开始加载财务统计数据')

    const response = await proxy.$api.finance.financeCensus()
    console.log('📊 财务统计响应:', response)

    if (response.code === '0' || response.code === 0) {
      censusData.value = response.data
      console.log('✅ 财务统计数据加载成功:', censusData.value)
    } else {
      console.error('❌ 获取财务统计数据失败:', response.msg)
      ElMessage.error(response.msg || '获取财务统计数据失败')
    }
  } catch (error) {
    console.error('❌ 加载财务统计数据异常:', error)
    ElMessage.error('获取财务统计数据失败')
  }
}

/**
 * 加载财务流水列表
 */
const loadFinanceList = async () => {
  try {
    loading.value = true
    console.log('🔍 开始加载财务流水列表，参数:', searchForm)

    // 构建请求参数，只传递有值的参数
    const requestParams = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 只有在搜索时才添加其他参数
    if (searchForm.type !== null) requestParams.type = searchForm.type
    if (searchForm.userId) requestParams.userId = parseInt(searchForm.userId)
    if (searchForm.coachId) requestParams.coachId = parseInt(searchForm.coachId)
    if (searchForm.startTime) requestParams.startTime = searchForm.startTime
    if (searchForm.endTime) requestParams.endTime = searchForm.endTime

    const response = await proxy.$api.finance.list(requestParams)
    console.log('📋 财务流水列表响应:', response)

    if (response.code === '200') {
      tableData.value = response.data.list || []
      total.value = response.data.totalCount || 0

      console.log(`✅ 财务流水列表加载成功，共 ${total.value} 条数据`)
    } else {
      ElMessage.error(response.msg || '获取财务流水列表失败')
    }
  } catch (error) {
    console.error('❌ 加载财务流水列表失败:', error)
    ElMessage.error('获取财务流水列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  searchForm.pageNum = 1
  loadFinanceList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    pageNum: 1,
    pageSize: 10,
    type: null,
    userId: '',
    coachId: '',
    startTime: '',
    endTime: ''
  })
  timeRange.value = []
  loadFinanceList()
}

/**
 * 导出财务流水列表 - POST方法，传递JSON body参数
 */
const handleExport = async () => {
  try {
    exportLoading.value = true
    console.log('📤 开始导出财务流水Excel...')

    // 构建导出参数JSON对象
    const exportParams = {}

    // 添加流水类型
    if (searchForm.type !== '' && searchForm.type !== null && searchForm.type !== undefined) {
      exportParams.type = parseInt(searchForm.type)
    }

    // 添加用户ID
    if (searchForm.userId !== '' && searchForm.userId !== null && searchForm.userId !== undefined) {
      exportParams.userId = parseInt(searchForm.userId)
    }

    // 添加师傅ID
    if (searchForm.coachId !== '' && searchForm.coachId !== null && searchForm.coachId !== undefined) {
      exportParams.coachId = parseInt(searchForm.coachId)
    }

    // 添加时间范围
    if (searchForm.startTime !== '' && searchForm.startTime !== null && searchForm.startTime !== undefined) {
      exportParams.startTime = searchForm.startTime
    }

    if (searchForm.endTime !== '' && searchForm.endTime !== null && searchForm.endTime !== undefined) {
      exportParams.endTime = searchForm.endTime
    }

    console.log('📤 导出参数:', exportParams)

    // 使用fetch发送POST请求下载文件 - 使用环境变量
    const token = sessionStorage.getItem('minitk')
    const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
    const exportUrl = `${baseUrl}/api/admin/finance/export`

    console.log('📤 导出URL:', exportUrl)

    const response = await fetch(exportUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      body: JSON.stringify(exportParams)
    })

    if (response.ok) {
      // 检查响应内容类型
      const contentType = response.headers.get('Content-Type')

      // 如果是JSON响应，说明可能是错误信息
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json()
        console.error('❌ 导出返回错误:', errorData)

        if (errorData.code === '-1' || errorData.code === -1) {
          // 显示具体的错误信息
          const errorMsg = errorData.msg || '导出失败'
          ElMessage.error(`导出失败: ${errorMsg}`)

          // 如果是数据库字段映射错误，给出更友好的提示
          if (errorMsg.includes('ResultMapException') || errorMsg.includes('column')) {
            ElMessage.warning('后端数据库字段映射异常，请联系技术人员修复')
          }
        } else {
          ElMessage.error(errorData.msg || '导出失败')
        }
        return
      }

      // 获取文件名（从响应头或使用默认名称）
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = `财务流水导出_${new Date().toISOString().slice(0, 10)}.xlsx`

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      }

      // 创建blob并下载
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL对象
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功，请查看浏览器下载')
      console.log('✅ 导出财务流水Excel成功')
    } else {
      // 尝试解析错误响应
      try {
        const errorText = await response.text()
        console.error('❌ 导出HTTP错误:', response.status, response.statusText, errorText)

        // 尝试解析JSON错误信息
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.msg) {
            ElMessage.error(`导出失败: ${errorData.msg}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        } catch (parseError) {
          throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
        }
      } catch (textError) {
        throw new Error(`导出失败: HTTP ${response.status} ${response.statusText}`)
      }
    }

  } catch (error) {
    console.error('❌ 导出财务流水Excel异常:', error)
    ElMessage.error('导出失败，请稍 后重试')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 分页大小变化
 */
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.pageNum = 1
  loadFinanceList()
}

/**
 * 当前页变化
 */
const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  loadFinanceList()
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 财务流水管理页面初始化')
  loadFinanceList()
  loadCensusData()
})
</script>

<style scoped>
/* ===== 核心样式实现 ===== */

/* 1. 页面容器 - 基础布局 */
.page-container {
  padding: 0px;
}

/* 2. 内容容器 - 白色背景 + 圆角 + 阴影 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

}

/* 2.1 统计卡片样式 - 使用统一规范 */
.stats-cards {
  margin-bottom: 20px;
}

/* 统计内容布局 */
.stat-content {
  text-align: center;
  padding: 10px 0;
}

.stat-card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 详细统计卡片样式 */
.detail-stats-cards {
  margin-bottom: 20px;
}

.detail-stat-card {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.detail-stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.stat-items {
  padding: 10px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item .stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-item .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  margin: 0;
}

.stat-item .stat-count {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

/* 3. 搜索表单 - 灰色背景区域 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form .el-form-item:last-child {
  margin-right: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 4. 表格容器 - 关键的阴影和圆角效果 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  width: 100%;
}

/* 5. 表格样式 - 深度选择器覆盖Element Plus默认样式 */
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}

/* 表头样式 */
.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

/* 表格内容样式 */
.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

/* 行悬停效果 */
.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 6. 价格样式 */
.price-positive {
  color: #67c23a;
  font-weight: 600;
}

.price-negative {
  color: #f56c6c;
  font-weight: 600;
}

.price-neutral {
  color: #909399;
  font-weight: 600;
}

/* 7. 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 8. 标签样式优化 */
.el-tag {
  font-size: 12px;
}

/* 9. 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 15px;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }
}

/* 10. 加载状态优化 */
:deep(.el-loading-mask) {
  border-radius: 8px;
}
</style>